services:
  # PostgreSQL 服务
  postgres:
    image: postgres:14-alpine  # 使用一个较新的、轻量的PostgreSQL镜像
    container_name: pan_so_postgres
    environment:
      - POSTGRES_USER=pan_so_user  # 设置数据库用户名
      - POSTGRES_PASSWORD=Wsk1998107.  # 设置数据库密码 (请修改为您自己的密码)
      - POSTGRES_DB=pan_so_db  # 自动创建的数据库名称
      # 性能优化配置
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"  # 将容器的5432端口映射到您主机的5432端口
    volumes:
      - postgres_data:/var/lib/postgresql/data  # 将数据持久化到Docker卷中，防止容器关闭后数据丢失
      - ./postgresql.conf:/etc/postgresql/postgresql.conf:ro  # 自定义配置文件
    restart: unless-stopped
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G    # 限制内存使用1.5GB
          cpus: '1.0'     # 限制CPU使用1核
        reservations:
          memory: 512M    # 预留512MB内存
          cpus: '0.3'     # 预留0.3核CPU
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pan_so_user -d pan_so_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 服务
  redis:
    image: redis:7-alpine  # 使用一个轻量的Redis镜像
    container_name: pan_so_redis
    ports:
      - "6379:6379"  # 将容器的6379端口映射到您主机的6379端口
    restart: unless-stopped

  # Meilisearch 服务
  meilisearch:
    image: getmeili/meilisearch:latest  # 使用本地已有镜像
    container_name: pan_so_meilisearch
    environment:
      - MEILI_MASTER_KEY=M3lPuLTZeJeA7urKBe6YN7nAPJxBZxA7Lr7kamVXW_k  # 生产环境安全密钥
      - MEILI_ENV=production
      - MEILI_MAX_INDEXING_MEMORY=3GB  # 增加索引内存使用（针对110万记录）
      - MEILI_MAX_INDEXING_THREADS=3   # 增加索引线程数
      - MEILI_BATCH_SIZE=1000           # 匹配我们的优化批次大小
      - MEILI_LOG_LEVEL=INFO           # 添加日志级别
      - MEILI_DUMP_DIR=/meili_data/dumps  # 指定备份目录
      - MEILI_SNAPSHOT_DIR=/meili_data/snapshots  # 指定快照目录
    ports:
      - "7700:7700"  # 映射Meilisearch默认端口
    volumes:
      - meilisearch_data:/meili_data   # 持久化数据
    restart: unless-stopped
    # 资源限制 - 针对大数据集索引优化
    deploy:
      resources:
        limits:
          memory: 6G      # 增加内存限制以支持大索引
          cpus: '3'       # 增加CPU限制以提高索引性能
        reservations:
          memory: 2G      # 增加内存预留
          cpus: '1.0'     # 增加CPU预留
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7700/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:      # 定义一个卷来持久化数据库数据
  meilisearch_data:   # 定义一个卷来持久化Meilisearch数据